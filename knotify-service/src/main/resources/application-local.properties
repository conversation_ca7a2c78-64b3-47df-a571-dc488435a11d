#spring.profiles.active=local
kiki.notify.saas-id=kiki
debug=true
logging.level.root=info
grpc.port=30880
kiki.ignite.namespace=kikitrade
kiki.ignite.applicationName=knotify
kiki.ignite.local=true
kiki.ignite.environment-namespace=kikitrade-dev
#ots
kiki.ots.endpoint=@knotify.kiki.ots.endpoint@
kiki.ots.instance-name=@knotify.kiki.ots.instance-name@
# market redis
spring.redis-market.host=localhost
spring.redis-market.password=
spring.redis-market.port=6379
spring.redis-market.lettuce.pool.max-active=5

################ market redis cluster ##############
spring.market-redis.timeout=5000
spring.market-redis.cluster.nodes=r-t4nmenbh4cve3k0c9f.redis.singapore.rds.aliyuncs.com:6379
spring.market-redis.password=XnRT3V9C
spring.market-redis.cluster.max-redirects=3
spring.market-redis.lettuce.cluster.refresh.adaptive=true
spring.market-redis.lettuce.cluster.refresh.period=PT15S

# ??????redis core? ?????????
spring.redis-notify.host=exkikidevo.redis.singapore.rds.aliyuncs.com
spring.redis-notify.database=5
spring.redis-notify.password=Credit2021Admin
spring.redis-notify.port=6379
spring.redis-notify.lettuce.pool.max-active=4
# dubbo config
dubbo.application.name=knotify
dubbo.application.id=knotify
dubbo.application.version=1.0.0
dubbo.protocol.server=netty
dubbo.protocol.name=dubbo
dubbo.protocol.port=20881
dubbo.protocol.threadpool=fixed
dubbo.protocol.threads=50
dubbo.protocol.queues=1000
dubbo.registry.address=zookeeper://localhost:2181
dubbo.consumer.filter=tracing
dubbo.provider.filter=tracing

#ons
kiki.ons.address=http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
kiki.ons.group-id=GID_NOTIFY_KIKI_DEV
kiki.ons.env=_DEV
kiki.ons.tag=dev
#tencent.ons-topic-push=T_NOTIFY_KIKI_MSG_PUSH_DEV

spring.zipkin.baseUrl=http://api.dipbit.xyz:9411
spring.sleuth.sampler.percentage=0.1

# knotify
knotify.task.numbersPerBatch=10
knotify.template.cache.enable=true
knotify.template.cache.TTLms=300000
knotify.social.key=0XtdLsYulHv0XCDB+Rm0l5dDt8le1xoONjFUSkX6GsA=
knotify.history.start.time=1663060895158
saasId=kiki
# task executor
spring.task.execution.threadNamePrefix=NotifyConsumer-
spring.task.execution.pool.max-size=32
spring.task.execution.pool.queue-capacity=1000
spring.task.execution.pool.keep-alive=10s

#email configuration
email.acs-region-id=@email.acs-region-id@
email.acs-end-point-name=@email.acs-end-point-name@
email.aliyun-product=Dm
email.aliyun-domain=@email.aliyun-domain@
email.sender-address.kiki=@email.sender-address@
email.sender-alias.kiki=KIKITRADE
email.signature=KIKITRADE
email.aws-sender=<EMAIL>
email.aws-region=us-east-1
email.default-language=en
email.sum-limit-count-every-day=10000
email.sum-limit-count-every-hour=5000
email.receiver-limit-count-every-day=30
email.receiver-limit-count-every-hour=15
sms.acs-region-id=cn-hangzhou
sms.aliyun-domain=dysmsapi.aliyuncs.com
sms.aliyun-product=Dysmsapi
sms.aliyun-action=SendMessageToGlobe
sms.aliyun-version=2017-05-25
#termii
sms.termiisms-baseurl=https://termii.com
sms.termiisms-apikey=TLFqr7bnpW4iB3F2WpU9b1GVUjDiwRgjdokBvjYcoZY5EyLamlqiXncnaM3jL9
#telesign
sms.telesign-customer-Id=032BBEA6-E8CD-402E-AE91-C0248A20BCA3
sms.telesign-apikey=nUxZBlldN2gBks30SqTFtOpA8oTHkamOa7NnR9NXX+zhbIPZHTIGkdF+x5aFw4zaGXxxbVIaW2M9BisuDN2Wig==
sms.telesign-rest-Endpoint=https://rest-ww.telesign.com

sms.sum-limit-count-every-day=10000
sms.sum-limit-count-every-hour=5000
sms.receiver-limit-count-every-day=30
sms.receiver-limit-count-every-hour=15
sms.block-score.86=450
sms.block-score.1=0
sms.check-phone-risk-open=true
sms.check-phone-risk-country-code=86,1
#?? ?? ??? ???? ??? ?? ???? ???? ?? ???
sms.white-phone-number-prefix=+852,+886,+65,+60,+63,+44,+61,+234,+233,+254

#################elasticjob config ##################
elasticjob.reg-center.server-lists=localhost:2181
elasticjob.reg-center.namespace=notify/jobs
elasticjob.reg-center.base-sleep-time-milliseconds=1000
elasticjob.reg-center.max-sleep-time-milliseconds=3000
elasticjob.reg-center.max-retries=5
elasticjob.reg-center.session-timeout-milliseconds=10000
elasticjob.reg-center.connection-timeout-milliseconds=10000
elasticjob.model=env
elasticjob.envs.green=close

## sharding refresh job
## ReceiveStatusJob job
elasticjob.jobs.receiveStatusJob.elastic-job-class=com.kikitrade.knotify.schedule.ReceiveStatusJob
elasticjob.jobs.receiveStatusJob.cron=0 */10 * * * ?
elasticjob.jobs.receiveStatusJob.sharding-total-count=1
elasticjob.jobs.receiveStatusJob.sharding-item-parameters=0=0

elasticjob.jobs.dissolveChatGroupJob.elastic-job-class=com.kikitrade.knotify.schedule.DissolveChatGroupJob
elasticjob.jobs.dissolveChatGroupJob.jobParameter=24
elasticjob.jobs.dissolveChatGroupJob.cron=0 */10 * * * ?
elasticjob.jobs.dissolveChatGroupJob.sharding-total-count=1
elasticjob.jobs.dissolveChatGroupJob.sharding-item-parameters=0=0

# getBalance job
elasticjob.jobs.BalanceAlertJob.elastic-job-class=com.kikitrade.knotify.schedule.BalanceAlertJob
elasticjob.jobs.BalanceAlertJob.cron=0 0 23 * * ?
elasticjob.jobs.BalanceAlertJob.sharding-total-count=1
elasticjob.jobs.BalanceAlertJob.sharding-item-parameters=0=0

tencent.domain=https://adminapisgp.im.qcloud.com
tencent.app-id=********
tencent.identifier=administrator
tencent.key=c9db502243615e9602ee0407fa1c6eca2ae0d3127900b2c966fffef5c191ef76
tencent.support-staff.C2C=c2c_admin
##ç³»ç»ç¨æ·
tencent.system-account.TAG1=kiki-dev-trade
tencent.system-account.TAG3=kiki-dev-wallet
tencent.system-account.TAG4=kiki-dev-financing
tencent.system-account.TAG5=kiki-dev-activity
tencent.system-account.TAG6=kiki-dev-c2c-trade
tencent.system-account.TAG7=kiki-dev-only-push
##pushConfigï¼å¤è¯­è¨ï¼
push.support-locales=en_us,zh_cn,zh_hk
push.default-language=en-us
push.locales.zh-tw=zh-hk
##pushConfigï¼ç¨æ·è®¾ç½®ï¼
push.business-relation.social=social_at,social_interaction,social_follow,social_like
push.business-relation.trade=trade_clear,trade_market
push.business-relation.wallet=wallet_crypto,wallet_circle,wallet_quickpay
push.business-relation.activity=activity_award
push.business-relation.financing=financing_flexible,financing_fixed
push.business-relation.c2cTrade=c2cTrade_c2c
##pushConfigï¼ä¸å¡åè´¦æ·çæ å°ï¼
##æ ç­¾æ å°(ç´æ¥ä»æä¸¾ä¸­å¯ä»¥æ å°)businessTag
push.business-tag.trade=TAG1
push.business-tag.wallet=TAG3
push.business-tag.financing=TAG4
push.business-tag.activity=TAG5
push.business-tag.c2cTrade=TAG6
push.business-tag.onlyPush=TAG7
###onlymsg|onlypush
push.only-msg-title-key.titlekey1=0
push.receiver-limit-count-every-min=200
push.sum-limit-count-every-min=1000
sms.new-match-inuse=true
sms.infobip-open=true
sms.channel-config=[]
sms.channel-rule=[]
balance.alert-open=true

#agora
agora.app-key=********************************
agora.app-secret=********************************
agora.cloud-recording-enable=true
agora.storage.vendor=S3
agora.storage.app-key=********************
agora.storage.app-secret=C5Ch12Ju5vxQcrZa6XxCoV+vqB8eFYDBS8JjXBx7
agora.storage.bucket=duom-dev-recording
agora.apps[0].app-id=********************************
agora.apps[0].app-certificate=********************************
agora.apps[0].domain=https://api.agora.io
agora.apps[0].type=Chatroom
agora.apps[1].app-id=********************************
agora.apps[1].app-certificate=********************************
agora.apps[1].domain=https://api.agora.io
agora.apps[1].type=Live_Stream

push.business.only-push=trade_market

# smtpæå¡å¨å°å
email.smtp-host=smtp.163.com
# smtpæå¡å¨ç«¯å£
email.smtp-port=465
#åä»¶äººé®ç®±
email.smtp-username=
# smtpææç ï¼éé®ç®±å¯ç 
email.smtp-password=GUYRRVLUNQZVNCTA
# åä»¶äººæµç§°
email.smtp-nickname=é®ä»¶æµç§°test
# æ¯å¦æ¯æå¤ç§æ·ï¼å¦ææ¯æåä»¥ä¸æ ééç½®
knotify.email.support.multi-tenant=false